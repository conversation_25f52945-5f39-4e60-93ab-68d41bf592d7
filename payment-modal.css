/* Payment Modal Styles */
.payment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

.payment-modal-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 480px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    animation: slideUp 0.3s ease-out;
}

.payment-modal h3 {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
}

.payment-modal p {
    margin: 0 0 24px 0;
    font-size: 16px;
    line-height: 1.6;
    color: #6b7280;
}

.payment-modal-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
}

.payment-modal button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.btn-cancel {
    background: #f3f4f6;
    color: #6b7280;
}

.btn-cancel:hover {
    background: #e5e7eb;
    color: #4b5563;
}

.btn-continue {
    background: #6366f1;
    color: white;
}

.btn-continue:hover {
    background: #5856eb;
    transform: translateY(-1px);
}

.btn-close {
    background: #6366f1;
    color: white;
    width: 100%;
}

.btn-close:hover {
    background: #5856eb;
    transform: translateY(-1px);
}

/* Success Modal */
.payment-modal.success .payment-modal-content {
    border-top: 4px solid #10b981;
}

.success-icon {
    width: 64px;
    height: 64px;
    background: #10b981;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: bold;
    margin: 0 auto 24px auto;
    animation: successPulse 0.6s ease-out;
}

.payment-modal.success h3 {
    color: #10b981;
}

/* Error Modal */
.payment-modal.error .payment-modal-content {
    border-top: 4px solid #ef4444;
}

.error-icon {
    width: 64px;
    height: 64px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: bold;
    margin: 0 auto 24px auto;
    animation: errorShake 0.6s ease-out;
}

.payment-modal.error h3 {
    color: #ef4444;
}

.payment-modal.error .btn-close {
    background: #ef4444;
}

.payment-modal.error .btn-close:hover {
    background: #dc2626;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes successPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-modal-content {
        padding: 24px;
        margin: 16px;
    }
    
    .payment-modal h3 {
        font-size: 20px;
    }
    
    .payment-modal p {
        font-size: 14px;
    }
    
    .payment-modal-buttons {
        flex-direction: column;
    }
    
    .payment-modal button {
        width: 100%;
        margin: 0;
    }
    
    .success-icon,
    .error-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .payment-modal-content {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .payment-modal h3 {
        color: #f9fafb;
    }
    
    .payment-modal.success h3 {
        color: #10b981;
    }
    
    .payment-modal.error h3 {
        color: #ef4444;
    }
    
    .payment-modal p {
        color: #d1d5db;
    }
    
    .btn-cancel {
        background: #374151;
        color: #d1d5db;
    }
    
    .btn-cancel:hover {
        background: #4b5563;
        color: #f9fafb;
    }
}

/* Loading state for payment buttons */
.payment-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.payment-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Currency conversion notice styling */
.payment-modal .currency-conversion {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    text-align: left;
}

.payment-modal .currency-conversion h4 {
    margin: 0 0 8px 0;
    color: #0ea5e9;
    font-size: 14px;
    font-weight: 600;
}

.payment-modal .currency-conversion p {
    margin: 0;
    font-size: 14px;
    color: #0369a1;
}

/* Payment button enhancements */
#buy-premium-btn {
    position: relative;
    overflow: hidden;
}

#buy-premium-btn small {
    display: block;
    font-size: 12px;
    opacity: 0.8;
    margin-top: 4px;
}

/* Accessibility improvements */
.payment-modal:focus-within {
    outline: none;
}

.payment-modal button:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .payment-modal {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .payment-modal-content {
        border: 2px solid #000;
    }
    
    .btn-continue,
    .btn-close {
        border: 2px solid #000;
    }
    
    .btn-cancel {
        border: 2px solid #666;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .payment-modal,
    .payment-modal-content,
    .success-icon,
    .error-icon {
        animation: none;
    }
    
    .payment-modal button {
        transition: none;
    }
}
