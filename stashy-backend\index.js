// index.js - Stashy Backend Cloud Functions

const express = require('express');
const cors = require('cors');
const Razorpay = require('razorpay');
const { Firestore } = require('@google-cloud/firestore');

const app = express();

// CORS configuration - Allow your website domain
app.use(cors({
    origin: ['https://stashyapp.com', 'http://localhost:3000', 'http://127.0.0.1:5500', 'http://localhost:8000', 'http://127.0.0.1:8000'],
    credentials: true
}));

app.use(express.json());

// --- CONFIGURATION ---
// CRITICAL: All secrets must be configured as environment variables in Google Cloud Functions
const RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID;
const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;
const RAZORPAY_WEBHOOK_SECRET = process.env.RAZORPAY_WEBHOOK_SECRET;

// Validate required environment variables
const requiredEnvVars = {
    RAZORPAY_KEY_ID,
    RAZORPAY_KEY_SECRET,
    RAZORPAY_WEBHOOK_SECRET
};

const missingVars = Object.entries(requiredEnvVars)
    .filter(([key, value]) => !value)
    .map(([key]) => key);

if (missingVars.length > 0) {
    console.error('❌ CRITICAL: Missing required environment variables:', missingVars);
    console.error('Please configure these in your Google Cloud Function environment settings.');
    process.exit(1);
}

console.log('✅ All required environment variables are configured');

// Initialize Firestore
const firestore = new Firestore({
    projectId: 'stashy-app-1e5d1'
});

// Initialize Razorpay
const razorpay = new Razorpay({
    key_id: RAZORPAY_KEY_ID,
    key_secret: RAZORPAY_KEY_SECRET,
});

/**
 * Health check endpoint
 */
app.get('/', (req, res) => {
    res.json({
        status: 'Stashy API is running!',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Test endpoint removed for security - use proper monitoring instead

/**
 * Endpoint to create a Razorpay order.
 * The website will call this when a user clicks "Upgrade".
 * Now supports currency information for international users.
 */
app.post('/create-order', async (req, res) => {
    console.log('Creating order for user:', req.body);

    const { amount, currency = 'INR', notes = {} } = req.body;

    if (!amount || amount <= 0) {
        return res.status(400).json({ error: 'Valid amount is required' });
    }

    // Environment variables are validated at startup, so keys are guaranteed to be configured

    // Convert amount to paise for Razorpay (multiply by 100)
    const amountInPaise = Math.round(amount * 100);

    const options = {
        amount: amountInPaise,
        currency: currency, // Always INR for Razorpay, but we track display currency in notes
        receipt: `receipt_stashy_${Date.now()}`,
        notes: {
            ...notes,
            created_at: new Date().toISOString(),
            source: 'stashy_website'
        },
    };

    try {
        console.log('Creating Razorpay order with options:', options);
        const order = await razorpay.orders.create(options);
        console.log('Order created successfully:', order.id);

        res.json({
            id: order.id,
            currency: order.currency,
            amount: order.amount,
            notes: order.notes
        });
    } catch (error) {
        console.error('Error creating Razorpay order:', error);
        console.error('Error details:', error.message, error.statusCode);
        res.status(500).json({
            error: 'Failed to create order',
            details: error.message
        });
    }
});

/**
 * Endpoint to verify payment signature
 * Called by frontend after payment completion
 */
app.post('/verify-payment', async (req, res) => {
    console.log('Verifying payment:', req.body);

    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
        return res.status(400).json({ error: 'Missing required payment parameters' });
    }

    try {
        // Verify payment signature
        const body = razorpay_order_id + "|" + razorpay_payment_id;
        const expectedSignature = require('crypto')
            .createHmac('sha256', RAZORPAY_KEY_SECRET)
            .update(body.toString())
            .digest('hex');

        if (expectedSignature === razorpay_signature) {
            console.log('Payment signature verified successfully');

            // Fetch payment details to get user information
            const payment = await razorpay.payments.fetch(razorpay_payment_id);
            const order = await razorpay.orders.fetch(razorpay_order_id);

            // Log payment details for currency tracking
            console.log('Payment completed:', {
                payment_id: razorpay_payment_id,
                order_id: razorpay_order_id,
                amount: payment.amount,
                currency: payment.currency,
                display_currency: order.notes?.display_currency,
                user_country: order.notes?.user_country
            });

            res.json({
                success: true,
                payment_id: razorpay_payment_id,
                order_id: razorpay_order_id
            });
        } else {
            console.error('Payment signature verification failed');
            res.status(400).json({ error: 'Invalid payment signature' });
        }
    } catch (error) {
        console.error('Error verifying payment:', error);
        res.status(500).json({ error: 'Payment verification failed' });
    }
});

/**
 * Endpoint to handle the Razorpay webhook after a successful payment.
 * Razorpay will call this, NOT your website.
 * Now includes currency tracking for analytics.
 */
app.post('/webhook', async (req, res) => {
    console.log('Webhook received:', req.headers, req.body);

    const signature = req.headers['x-razorpay-signature'];
    const body = JSON.stringify(req.body);

    try {
        // Securely verify the webhook signature
        const isAuthentic = Razorpay.utils.verifyWebhookSignature(body, signature, RAZORPAY_WEBHOOK_SECRET);

        if (isAuthentic) {
            console.log('Webhook verified successfully.');
            const { event, payload } = req.body;

            // Handle payment.captured event
            if (event === 'payment.captured' && payload.payment && payload.payment.entity) {
                const payment = payload.payment.entity;
                const paymentId = payment.id;
                const orderId = payment.order_id;

                console.log(`Processing payment capture: ${paymentId}`);

                // Fetch order details to get notes
                const order = await razorpay.orders.fetch(orderId);
                const googleUserId = order.notes?.googleUserId;

                if (!googleUserId) {
                    console.error('Webhook Error: googleUserId not found in order notes.');
                    return res.status(400).send('Webhook Error: User ID missing');
                }

                console.log(`Processing premium activation for user: ${googleUserId}`);

                // Determine subscription duration based on amount
                const amount = payment.amount / 100; // Convert from paise to rupees
                let subscriptionMonths = 1; // Default to monthly
                let planType = 'premium_monthly';

                // If amount suggests annual plan (around 350*12 = 4200)
                if (amount >= 4000) {
                    subscriptionMonths = 12;
                    planType = 'premium_annual';
                }

                // Update Firestore Database
                const userRef = firestore.collection('users').doc(googleUserId);
                const expiryDate = new Date();
                expiryDate.setMonth(expiryDate.getMonth() + subscriptionMonths);

                const userData = {
                    premium: true,
                    expiryDate: expiryDate.toISOString(),
                    paymentId: paymentId,
                    orderId: orderId,
                    lastUpdated: new Date().toISOString(),
                    plan: planType,
                    paymentAmount: amount,
                    paymentCurrency: payment.currency,
                    displayCurrency: order.notes?.display_currency || 'INR',
                    userCountry: order.notes?.user_country || 'IN'
                };

                await userRef.set(userData, { merge: true });

                console.log(`User ${googleUserId} successfully upgraded to ${planType}.`);

                // Log for analytics
                console.log('Payment analytics:', {
                    user_id: googleUserId,
                    plan: planType,
                    amount: amount,
                    currency: payment.currency,
                    display_currency: order.notes?.display_currency,
                    country: order.notes?.user_country,
                    payment_id: paymentId
                });
            }

            res.status(200).send('Webhook processed successfully.');
        } else {
            console.error('Webhook Error: Invalid signature');
            res.status(400).send('Invalid signature');
        }
    } catch (error) {
        console.error('Error processing webhook:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Endpoint for the Chrome extension to check a user's premium status.
 */
app.get('/checkStatus/:googleUserId', async (req, res) => {
    const { googleUserId } = req.params;
    console.log('Checking status for user:', googleUserId);
    
    if (!googleUserId) {
        return res.status(400).json({ error: 'googleUserId is required' });
    }

    try {
        const userRef = firestore.collection('users').doc(googleUserId);
        const doc = await userRef.get();

        if (!doc.exists) {
            console.log('User not found, returning free status');
            return res.json({ premium: false });
        }

        const userData = doc.data();
        const isPremium = userData.premium === true && new Date(userData.expiryDate) > new Date();
        
        console.log('User status:', { premium: isPremium, expiryDate: userData.expiryDate });
        
        res.json({
            premium: isPremium,
            expiryDate: userData.expiryDate || null,
            plan: userData.plan || 'free'
        });

    } catch (error) {
        console.error('Error checking user status:', error);
        res.status(500).json({ error: 'Failed to check status' });
    }
});

// Export the app for Cloud Functions
exports.stashyApi = app;
