/**
 * Payment Integration with Currency Detection
 * Handles Razorpay payments with dynamic currency conversion
 */

class PaymentIntegration {
    constructor() {
        this.razorpayKeyId = window.RAZORPAY_KEY_ID || 'rzp_test_your_key_here';
        this.backendUrl = window.BACKEND_URL || 'https://your-backend-url.com';
        this.currencyDetector = null;
        
        this.init();
    }
    
    init() {
        // Wait for currency detector to be available
        this.waitForCurrencyDetector();
        
        // Attach payment button events
        this.attachPaymentEvents();
    }
    
    waitForCurrencyDetector() {
        const checkForDetector = () => {
            if (window.currencyDetector) {
                this.currencyDetector = window.currencyDetector;
                this.updatePaymentButtons();
            } else {
                setTimeout(checkForDetector, 100);
            }
        };
        checkForDetector();
    }
    
    attachPaymentEvents() {
        // Premium plan button
        const premiumButton = document.getElementById('buy-premium-btn');
        if (premiumButton) {
            premiumButton.addEventListener('click', () => {
                this.initiatePremiumPayment();
            });
        }
        
        // CTA buttons
        const ctaButtons = document.querySelectorAll('.cta-button');
        ctaButtons.forEach(button => {
            if (button.textContent.includes('Trial') || button.textContent.includes('Premium')) {
                button.addEventListener('click', () => {
                    this.initiatePremiumPayment();
                });
            }
        });
        
        // Free plan button
        const freeButton = document.querySelector('.free-button');
        if (freeButton) {
            freeButton.addEventListener('click', () => {
                this.handleFreePlan();
            });
        }
    }
    
    updatePaymentButtons() {
        if (!this.currencyDetector) return;
        
        const pricing = this.currencyDetector.getCurrentPricing();
        
        // Update button text with pricing
        const premiumButton = document.getElementById('buy-premium-btn');
        if (premiumButton && pricing.currency !== 'INR') {
            const monthlyFormatted = this.currencyDetector.formatPrice(pricing.monthly, this.currencyDetector.currentCurrency);
            premiumButton.innerHTML = `Start 7-Day Free Trial<br><small>Then ${monthlyFormatted}/month</small>`;
        }
    }
    
    async initiatePremiumPayment() {
        try {
            if (!this.currencyDetector) {
                console.error('Currency detector not available');
                this.fallbackToINRPayment();
                return;
            }
            
            const pricing = this.currencyDetector.getCurrentPricing();
            
            // For non-INR currencies, we need to handle conversion
            if (pricing.currency !== 'INR') {
                await this.handleInternationalPayment(pricing);
            } else {
                await this.handleINRPayment(pricing);
            }
            
        } catch (error) {
            console.error('Payment initiation failed:', error);
            this.showPaymentError('Payment initialization failed. Please try again.');
        }
    }
    
    async handleINRPayment(pricing) {
        // Standard Razorpay flow for INR
        const orderData = await this.createOrder({
            amount: pricing.monthly * 100, // Razorpay expects amount in paise
            currency: 'INR'
        });
        
        this.openRazorpayCheckout(orderData, pricing);
    }
    
    async handleInternationalPayment(pricing) {
        // For international payments, we still process in INR but show local currency
        // This is because Razorpay primarily supports INR
        // You might want to integrate with Stripe or other international payment processors
        
        const inrPricing = {
            monthly: this.currencyDetector.basePrices.monthly,
            annual: this.currencyDetector.basePrices.annual,
            currency: 'INR',
            symbol: '₹'
        };
        
        // Show conversion notice to user
        const userConfirmed = await this.showCurrencyConversionNotice(pricing, inrPricing);
        if (!userConfirmed) return;
        
        const orderData = await this.createOrder({
            amount: inrPricing.monthly * 100,
            currency: 'INR',
            notes: {
                display_currency: pricing.currency,
                display_amount: pricing.monthly,
                user_country: this.currencyDetector.userCountry
            }
        });
        
        this.openRazorpayCheckout(orderData, inrPricing, pricing);
    }
    
    async showCurrencyConversionNotice(displayPricing, chargePricing) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'payment-modal';
            modal.innerHTML = `
                <div class="payment-modal-content">
                    <h3>Payment Currency Notice</h3>
                    <p>Your subscription will be displayed as <strong>${this.currencyDetector.formatPrice(displayPricing.monthly, this.currencyDetector.currentCurrency)}/month</strong> but charged as <strong>₹${chargePricing.monthly}/month</strong> (Indian Rupees) due to payment processor requirements.</p>
                    <p>The conversion rate used is approximate and may vary slightly at the time of payment.</p>
                    <div class="payment-modal-buttons">
                        <button class="btn-cancel">Cancel</button>
                        <button class="btn-continue">Continue with Payment</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            modal.querySelector('.btn-cancel').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
            
            modal.querySelector('.btn-continue').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(true);
            });
            
            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    resolve(false);
                }
            });
        });
    }
    
    async createOrder(orderData) {
        const response = await fetch(`${this.backendUrl}/create-order`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });
        
        if (!response.ok) {
            throw new Error('Failed to create order');
        }
        
        return await response.json();
    }
    
    openRazorpayCheckout(orderData, chargePricing, displayPricing = null) {
        const options = {
            key: this.razorpayKeyId,
            amount: orderData.amount,
            currency: orderData.currency,
            name: 'Stashy',
            description: 'Premium Subscription',
            image: '/public/owl_128x128.png',
            order_id: orderData.id,
            handler: (response) => {
                this.handlePaymentSuccess(response, displayPricing || chargePricing);
            },
            prefill: {
                name: '',
                email: '',
                contact: ''
            },
            notes: orderData.notes || {},
            theme: {
                color: '#6366f1'
            },
            modal: {
                ondismiss: () => {
                    this.handlePaymentCancel();
                }
            }
        };
        
        const rzp = new Razorpay(options);
        rzp.open();
    }
    
    async handlePaymentSuccess(response, pricing) {
        try {
            // Verify payment on backend
            const verification = await fetch(`${this.backendUrl}/verify-payment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_signature: response.razorpay_signature
                })
            });
            
            if (verification.ok) {
                this.showPaymentSuccess(pricing);
                this.trackPaymentSuccess(pricing);
            } else {
                throw new Error('Payment verification failed');
            }
            
        } catch (error) {
            console.error('Payment verification error:', error);
            this.showPaymentError('Payment verification failed. Please contact support.');
        }
    }
    
    handlePaymentCancel() {
        console.log('Payment cancelled by user');
        // Track cancellation for analytics
        if (window.gtag) {
            gtag('event', 'payment_cancel', {
                'currency': this.currencyDetector?.currentCurrency?.code || 'INR'
            });
        }
    }
    
    showPaymentSuccess(pricing) {
        const modal = document.createElement('div');
        modal.className = 'payment-modal success';
        modal.innerHTML = `
            <div class="payment-modal-content">
                <div class="success-icon">✓</div>
                <h3>Payment Successful!</h3>
                <p>Welcome to Stashy Premium! Your subscription is now active.</p>
                <p>You'll be charged ${this.currencyDetector?.formatPrice(pricing.monthly, this.currencyDetector?.currentCurrency) || '₹450'} monthly.</p>
                <button class="btn-close">Get Started</button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        modal.querySelector('.btn-close').addEventListener('click', () => {
            document.body.removeChild(modal);
            // Redirect to extension or dashboard
            window.location.href = '/';
        });
    }
    
    showPaymentError(message) {
        const modal = document.createElement('div');
        modal.className = 'payment-modal error';
        modal.innerHTML = `
            <div class="payment-modal-content">
                <div class="error-icon">✗</div>
                <h3>Payment Failed</h3>
                <p>${message}</p>
                <button class="btn-close">Try Again</button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        modal.querySelector('.btn-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }
    
    trackPaymentSuccess(pricing) {
        if (window.gtag) {
            gtag('event', 'purchase', {
                'transaction_id': Date.now().toString(),
                'value': pricing.monthly,
                'currency': pricing.currency,
                'items': [{
                    'item_id': 'stashy_premium',
                    'item_name': 'Stashy Premium',
                    'category': 'Subscription',
                    'quantity': 1,
                    'price': pricing.monthly
                }]
            });
        }
    }
    
    handleFreePlan() {
        // Redirect to Chrome Web Store or show installation instructions
        window.open('https://chrome.google.com/webstore/detail/stashy/your-extension-id', '_blank');
        
        if (window.gtag) {
            gtag('event', 'free_plan_selected', {
                'currency': this.currencyDetector?.currentCurrency?.code || 'INR'
            });
        }
    }
    
    fallbackToINRPayment() {
        // Fallback to INR payment if currency detection fails
        const pricing = {
            monthly: 450,
            annual: 350,
            currency: 'INR',
            symbol: '₹'
        };
        
        this.handleINRPayment(pricing);
    }
}

// Initialize payment integration when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.paymentIntegration = new PaymentIntegration();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PaymentIntegration;
}
