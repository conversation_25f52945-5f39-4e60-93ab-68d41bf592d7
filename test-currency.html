<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .pricing-test {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .price-card {
            flex: 1;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .price {
            font-size: 24px;
            font-weight: bold;
            color: #6366f1;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5856eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
    <link rel="stylesheet" href="currency-selector.css">
    <link rel="stylesheet" href="payment-modal.css">
</head>
<body>
    <div class="test-container">
        <h1>Currency Detection System Test</h1>
        
        <div class="test-section">
            <h3>System Status</h3>
            <div id="system-status" class="status loading">Initializing currency detection...</div>
            <div id="location-status" class="status loading">Detecting location...</div>
            <div id="rates-status" class="status loading">Fetching exchange rates...</div>
        </div>
        
        <div class="test-section">
            <h3>Detected Information</h3>
            <p><strong>Country:</strong> <span id="detected-country">-</span></p>
            <p><strong>Currency:</strong> <span id="detected-currency">-</span></p>
            <p><strong>Currency Symbol:</strong> <span id="detected-symbol">-</span></p>
            <p><strong>Flag:</strong> <span id="detected-flag">-</span></p>
        </div>
        
        <div class="test-section">
            <h3>Pricing Test</h3>
            <div class="pricing-test">
                <div class="price-card">
                    <h4>Free Plan</h4>
                    <div class="price free-price">₹0</div>
                    <p>/month</p>
                </div>
                <div class="price-card">
                    <h4>Premium Monthly</h4>
                    <div class="price monthly-price">₹450</div>
                    <p>/month</p>
                </div>
                <div class="price-card">
                    <h4>Premium Annual</h4>
                    <div class="price annual-price">₹350</div>
                    <p>/month</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Currency Selector Test</h3>
            <div id="currency-selector-container">
                <!-- Currency selector will be inserted here -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testCurrencyChange('US')">Test USD</button>
            <button onclick="testCurrencyChange('GB')">Test GBP</button>
            <button onclick="testCurrencyChange('EU')">Test EUR</button>
            <button onclick="testCurrencyChange('JP')">Test JPY</button>
            <button onclick="testCurrencyChange('IN')">Reset to INR</button>
            <button onclick="clearCache()">Clear Cache</button>
            <button onclick="testPayment()">Test Payment</button>
        </div>
        
        <div class="test-section">
            <h3>Exchange Rates</h3>
            <div id="exchange-rates">Loading...</div>
        </div>
        
        <div class="test-section">
            <h3>Debug Log</h3>
            <div id="debug-log" class="log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script src="currency-config.js"></script>
    <script src="currency-detector.js"></script>
    <script src="payment-integration.js"></script>
    
    <script>
        // Test utilities
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;
        
        function logToDebug(message, type = 'log') {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        // Override console methods to capture logs
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToDebug(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToDebug(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToDebug(args.join(' '), 'warn');
        };
        
        // Test functions
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function updateDetectedInfo() {
            if (window.currencyDetector) {
                const detector = window.currencyDetector;
                document.getElementById('detected-country').textContent = detector.userCountry || '-';
                document.getElementById('detected-currency').textContent = detector.currentCurrency?.code || '-';
                document.getElementById('detected-symbol').textContent = detector.currentCurrency?.symbol || '-';
                document.getElementById('detected-flag').textContent = detector.currentCurrency?.flag || '-';
                
                // Update exchange rates display
                const ratesDiv = document.getElementById('exchange-rates');
                if (detector.exchangeRates && Object.keys(detector.exchangeRates).length > 0) {
                    const ratesHtml = Object.entries(detector.exchangeRates)
                        .slice(0, 10) // Show first 10 rates
                        .map(([currency, rate]) => `<div>${currency}: ${rate}</div>`)
                        .join('');
                    ratesDiv.innerHTML = ratesHtml;
                } else {
                    ratesDiv.textContent = 'No exchange rates available';
                }
            }
        }
        
        function testCurrencyChange(countryCode) {
            if (window.currencyDetector) {
                window.currencyDetector.changeCurrency(countryCode);
                updateDetectedInfo();
                logToDebug(`Changed currency to ${countryCode}`);
            }
        }
        
        function clearCache() {
            localStorage.removeItem('stashy_exchange_rates');
            localStorage.removeItem('stashy_preferred_currency');
            logToDebug('Cache cleared');
            location.reload();
        }
        
        function testPayment() {
            if (window.paymentIntegration) {
                window.paymentIntegration.initiatePremiumPayment();
                logToDebug('Payment test initiated');
            }
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // Monitor currency detector initialization
        document.addEventListener('DOMContentLoaded', () => {
            logToDebug('DOM loaded, waiting for currency detector...');
            
            const checkDetector = () => {
                if (window.currencyDetector) {
                    updateStatus('system-status', 'Currency detection system loaded', 'success');
                    
                    // Monitor the detector's progress
                    const originalInit = window.currencyDetector.init;
                    const originalDetectLocation = window.currencyDetector.detectUserLocation;
                    const originalFetchRates = window.currencyDetector.fetchExchangeRates;
                    
                    window.currencyDetector.detectUserLocation = async function() {
                        updateStatus('location-status', 'Detecting location...', 'loading');
                        try {
                            await originalDetectLocation.call(this);
                            updateStatus('location-status', `Location detected: ${this.userCountry}`, 'success');
                            updateDetectedInfo();
                        } catch (error) {
                            updateStatus('location-status', 'Location detection failed', 'error');
                        }
                    };
                    
                    window.currencyDetector.fetchExchangeRates = async function() {
                        updateStatus('rates-status', 'Fetching exchange rates...', 'loading');
                        try {
                            await originalFetchRates.call(this);
                            updateStatus('rates-status', 'Exchange rates loaded', 'success');
                            updateDetectedInfo();
                        } catch (error) {
                            updateStatus('rates-status', 'Exchange rates failed', 'error');
                        }
                    };
                    
                    updateDetectedInfo();
                } else {
                    setTimeout(checkDetector, 100);
                }
            };
            
            checkDetector();
        });
    </script>
</body>
</html>
