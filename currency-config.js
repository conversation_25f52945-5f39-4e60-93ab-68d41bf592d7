/**
 * Currency Detection Configuration
 * Centralized configuration for currency detection and conversion
 */

window.CURRENCY_CONFIG = {
    // API Configuration
    apis: {
        geolocation: [
            { 
                url: 'https://ipapi.co/json/', 
                timeout: 3000,
                priority: 1,
                fields: { country: 'country_code' }
            },
            { 
                url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free', 
                timeout: 3000,
                priority: 2,
                fields: { country: 'country_code' }
            },
            { 
                url: 'https://ipinfo.io/json', 
                timeout: 3000,
                priority: 3,
                fields: { country: 'country' }
            }
        ],
        exchangeRates: [
            { 
                url: 'https://api.exchangerate-api.com/v4/latest/INR', 
                timeout: 5000,
                priority: 1,
                rateField: 'rates'
            },
            { 
                url: 'https://open.er-api.com/v6/latest/INR', 
                timeout: 5000,
                priority: 2,
                rateField: 'rates'
            },
            { 
                url: 'https://api.fixer.io/latest?base=INR&access_key=free', 
                timeout: 5000,
                priority: 3,
                rateField: 'rates'
            }
        ]
    },
    
    // Cache Configuration
    cache: {
        exchangeRatesKey: 'stashy_exchange_rates',
        preferredCurrencyKey: 'stashy_preferred_currency',
        exchangeRatesExpiry: 4 * 60 * 60 * 1000, // 4 hours
        backgroundUpdateInterval: 2 * 60 * 60 * 1000, // 2 hours
        maxRetries: 3,
        retryDelay: 1000 // 1 second
    },
    
    // Performance Configuration
    performance: {
        enableBackgroundUpdates: true,
        enableCaching: true,
        enableFallbacks: true,
        maxLoadTime: 10000, // 10 seconds max load time
        enableAnalytics: true
    },
    
    // Fallback Exchange Rates (updated as of 2025)
    fallbackRates: {
        'USD': 0.012,
        'EUR': 0.011,
        'GBP': 0.0095,
        'CAD': 0.016,
        'AUD': 0.018,
        'SGD': 0.016,
        'JPY': 1.8,
        'CHF': 0.011,
        'SEK': 0.13,
        'NOK': 0.13,
        'DKK': 0.082,
        'BRL': 0.067,
        'MXN': 0.24,
        'ARS': 12.5,
        'ZAR': 0.22,
        'KRW': 16.2,
        'CNY': 0.087,
        'HKD': 0.094,
        'TWD': 0.38,
        'THB': 0.42,
        'MYR': 0.056,
        'IDR': 185,
        'PHP': 0.68,
        'VND': 295,
        'AED': 0.044,
        'SAR': 0.045,
        'ILS': 0.045,
        'TRY': 0.35,
        'RUB': 1.2,
        'PLN': 0.049,
        'CZK': 0.28,
        'HUF': 4.4,
        'RON': 0.055,
        'BGN': 0.022,
        'HRK': 0.083,
        'RSD': 1.3,
        'UAH': 0.49,
        'EGP': 0.59,
        'NGN': 18.5,
        'KES': 1.9,
        'GHS': 0.15,
        'BDT': 1.3,
        'PKR': 3.4,
        'LKR': 3.6,
        'NPR': 1.6
    },
    
    // Rate Validation Rules
    validation: {
        usd: { min: 0.005, max: 0.02 },
        eur: { min: 0.008, max: 0.015 },
        gbp: { min: 0.007, max: 0.012 },
        jpy: { min: 1.0, max: 3.0 },
        krw: { min: 10, max: 25 }
    },
    
    // Analytics Configuration
    analytics: {
        trackCurrencyChanges: true,
        trackLocationDetection: true,
        trackPaymentCurrency: true,
        trackErrors: true
    },
    
    // Error Messages
    errorMessages: {
        locationFailed: 'Unable to detect your location. Defaulting to Indian Rupees.',
        ratesFailed: 'Unable to fetch current exchange rates. Using approximate rates.',
        paymentFailed: 'Payment processing failed. Please try again.',
        networkError: 'Network error. Please check your connection.',
        timeout: 'Request timed out. Please try again.'
    },
    
    // Feature Flags
    features: {
        enableCurrencySelector: true,
        enableAutoDetection: true,
        enableCurrencyConversionNotice: true,
        enableRealTimeRates: true,
        enableOfflineMode: false
    },
    
    // Regional Settings
    regions: {
        // European Union countries that use EUR
        euroZone: ['AT', 'BE', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PT', 'SK', 'SI', 'ES'],
        
        // Countries with special handling
        specialHandling: {
            'GB': { currency: 'GBP', note: 'United Kingdom' },
            'CH': { currency: 'CHF', note: 'Switzerland' },
            'NO': { currency: 'NOK', note: 'Norway' },
            'SE': { currency: 'SEK', note: 'Sweden' },
            'DK': { currency: 'DKK', note: 'Denmark' }
        }
    },
    
    // Development Settings
    development: {
        enableDebugLogs: false,
        enableMockData: false,
        mockCountry: 'US',
        mockRates: null,
        enableTestMode: false
    }
};

// Utility functions for configuration
window.CURRENCY_CONFIG.utils = {
    isEuroZone: function(countryCode) {
        return this.regions.euroZone.includes(countryCode);
    },
    
    hasSpecialHandling: function(countryCode) {
        return this.regions.specialHandling.hasOwnProperty(countryCode);
    },
    
    getValidationRule: function(currency) {
        return this.validation[currency.toLowerCase()] || null;
    },
    
    isValidRate: function(currency, rate) {
        const rule = this.getValidationRule(currency);
        if (!rule) return true; // No validation rule, assume valid
        return rate >= rule.min && rate <= rule.max;
    },
    
    getErrorMessage: function(errorType) {
        return this.errorMessages[errorType] || 'An error occurred.';
    },
    
    isFeatureEnabled: function(feature) {
        return this.features[feature] === true;
    },
    
    isDevelopment: function() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               this.development.enableTestMode;
    },
    
    shouldEnableDebugLogs: function() {
        return this.isDevelopment() && this.development.enableDebugLogs;
    }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.CURRENCY_CONFIG;
}
