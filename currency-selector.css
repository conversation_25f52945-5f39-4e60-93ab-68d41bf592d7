/* Currency Selector Styles */
.currency-selector {
    position: relative;
    display: inline-block;
    margin-top: 20px;
    z-index: 1000;
}

.currency-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 120px;
    justify-content: space-between;
}

.currency-button:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.currency-button:active {
    transform: translateY(0);
}

.currency-flag {
    font-size: 18px;
    line-height: 1;
}

.currency-code {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.currency-button i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.currency-button.active i {
    transform: rotate(180deg);
}

.currency-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
    min-width: 280px;
}

.currency-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.currency-section {
    padding: 16px 0;
}

.currency-section:not(:last-child) {
    border-bottom: 1px solid #f0f2f5;
}

.currency-section h4 {
    margin: 0 0 12px 0;
    padding: 0 16px;
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.currency-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.currency-option:hover {
    background-color: #f8fafc;
}

.currency-option:active {
    background-color: #e2e8f0;
}

.currency-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
}

.currency-name {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.2;
}

.currency-code {
    font-size: 12px;
    color: #6b7280;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .currency-selector {
        margin-top: 16px;
    }
    
    .currency-button {
        padding: 10px 14px;
        font-size: 13px;
        min-width: 100px;
    }
    
    .currency-dropdown {
        min-width: 260px;
        max-height: 300px;
    }
    
    .currency-option {
        padding: 10px 14px;
    }
    
    .currency-name {
        font-size: 13px;
    }
    
    .currency-code {
        font-size: 11px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .currency-dropdown {
        background: #1f2937;
        border-color: #374151;
    }
    
    .currency-section {
        border-color: #374151;
    }
    
    .currency-section h4 {
        color: #9ca3af;
    }
    
    .currency-option:hover {
        background-color: #374151;
    }
    
    .currency-option:active {
        background-color: #4b5563;
    }
    
    .currency-name {
        color: #f9fafb;
    }
    
    .currency-code {
        color: #9ca3af;
    }
}

/* Loading state styles */
.currency-loading {
    opacity: 0.6;
    pointer-events: none;
}

.currency-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Integration with existing pricing toggle */
.pricing-toggle-section .currency-selector {
    text-align: center;
    margin-top: 24px;
}

.pricing-toggle-section .currency-selector .currency-button {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
    color: #6366f1;
}

.pricing-toggle-section .currency-selector .currency-button:hover {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.3);
}

/* Smooth transitions for price updates */
.price {
    transition: all 0.3s ease;
}

.price.updating {
    opacity: 0.6;
    transform: scale(0.98);
}

/* Currency indicator in pricing cards */
.pricing-card .currency-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #6b7280;
    margin-left: 8px;
}

.pricing-card .currency-indicator .flag {
    font-size: 14px;
}

/* Accessibility improvements */
.currency-button:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

.currency-option:focus {
    outline: 2px solid #6366f1;
    outline-offset: -2px;
    background-color: #e0e7ff;
}

/* Animation for currency changes */
@keyframes priceUpdate {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1); }
}

.price-updating {
    animation: priceUpdate 0.5s ease-in-out;
}

/* Error state */
.currency-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    text-align: center;
}

/* Success state for currency change */
.currency-changed {
    position: relative;
}

.currency-changed::after {
    content: '✓';
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #10b981;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; transform: scale(0.8); }
    20%, 80% { opacity: 1; transform: scale(1); }
}
