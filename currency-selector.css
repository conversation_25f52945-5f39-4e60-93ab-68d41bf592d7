/* Currency Selector Styles */
.currency-selector {
    position: relative;
    display: inline-block;
    margin-top: 20px;
    z-index: 1000;
}

.currency-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px 18px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(12px);
    min-width: 140px;
    justify-content: space-between;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.4),
        0 3px 12px rgba(118, 75, 162, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.currency-button:hover {
    background: linear-gradient(135deg, #7c8df0 0%, #8b5fb8 100%);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.5),
        0 5px 18px rgba(118, 75, 162, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.currency-button:active {
    transform: translateY(-1px);
}

.currency-flag {
    font-size: 20px;
    line-height: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: transform 0.2s ease;
}

.currency-button:hover .currency-flag {
    transform: scale(1.1);
}

.currency-button .currency-code {
    font-weight: 700;
    letter-spacing: 0.8px;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.currency-button i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.currency-button.active i {
    transform: rotate(180deg);
}

.currency-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow:
        0 25px 80px rgba(102, 126, 234, 0.4),
        0 8px 32px rgba(118, 75, 162, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 420px;
    overflow-y: auto;
    z-index: 1001;
    min-width: 320px;
    backdrop-filter: blur(12px);

    /* Hide scrollbar but keep functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.currency-dropdown::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.currency-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}



.currency-section {
    padding: 8px 0;
}

.currency-section:not(:last-child) {
    border-bottom: 1px solid #f0f2f5;
}

.currency-section h4 {
    margin: 0 0 8px 0;
    padding: 10px 16px 6px 16px;
    font-size: 11px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 8px;
    margin: 8px 12px 4px 12px;
    position: sticky;
    top: 8px;
    z-index: 10;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.currency-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 3px 12px;
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);
}

.currency-option:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    transform: translateX(4px) translateY(-1px);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 3px 12px rgba(118, 75, 162, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.currency-option:active {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(2px) translateY(0);
}

.currency-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
    flex: 1;
    min-width: 0; /* Prevent overflow */
}

.currency-name {
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.currency-code {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    letter-spacing: 0.8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.currency-option .currency-flag {
    font-size: 22px;
    line-height: 1;
    min-width: 28px;
    text-align: center;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: transform 0.2s ease;
}

.currency-option:hover .currency-flag {
    transform: scale(1.1);
}

/* Enhanced visual effects */
.currency-dropdown::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 24px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #667eea;
    z-index: 1002;
    filter: drop-shadow(0 -2px 4px rgba(102, 126, 234, 0.3));
}

.currency-dropdown::after {
    content: '';
    position: absolute;
    top: -12px;
    left: 22px;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 12px solid rgba(255, 255, 255, 0.2);
    z-index: 1001;
}

/* Improved section styling */
.currency-section:first-child {
    padding-top: 12px;
}

.currency-section:last-child {
    padding-bottom: 12px;
}

/* Better option styling */
.currency-option:first-child {
    margin-top: 4px;
}

.currency-option:last-child {
    margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .currency-selector {
        margin-top: 16px;
    }

    .currency-button {
        padding: 10px 14px;
        font-size: 13px;
        min-width: 100px;
    }

    .currency-dropdown {
        min-width: 280px;
        max-height: 350px;
        left: -20px;
        right: -20px;
    }

    .currency-dropdown::before,
    .currency-dropdown::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .currency-option {
        padding: 12px 16px;
        margin: 2px 12px;
    }

    .currency-name {
        font-size: 13px;
    }

    .currency-code {
        font-size: 11px;
    }

    .currency-section h4 {
        padding: 8px 16px 4px 16px;
        font-size: 10px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .currency-dropdown {
        background: #1f2937;
        border-color: #374151;
    }
    
    .currency-section {
        border-color: #374151;
    }
    
    .currency-section h4 {
        color: #9ca3af;
    }
    
    .currency-option:hover {
        background-color: #374151;
    }
    
    .currency-option:active {
        background-color: #4b5563;
    }
    
    .currency-name {
        color: #f9fafb;
    }
    
    .currency-code {
        color: #9ca3af;
    }
}

/* Loading state styles */
.currency-loading {
    opacity: 0.6;
    pointer-events: none;
}

.currency-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Integration with existing pricing toggle */
.pricing-toggle-section .currency-selector {
    text-align: center;
    margin-top: 24px;
}

.pricing-toggle-section .currency-selector .currency-button {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
    color: #6366f1;
}

.pricing-toggle-section .currency-selector .currency-button:hover {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.3);
}

/* Smooth transitions for price updates */
.price {
    transition: all 0.3s ease;
}

.price.updating {
    opacity: 0.6;
    transform: scale(0.98);
}

/* Currency indicator in pricing cards */
.pricing-card .currency-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #6b7280;
    margin-left: 8px;
}

.pricing-card .currency-indicator .flag {
    font-size: 14px;
}

/* Accessibility improvements */
.currency-button:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

.currency-option:focus {
    outline: 2px solid #6366f1;
    outline-offset: -2px;
    background-color: #e0e7ff;
}

/* Animation for currency changes */
@keyframes priceUpdate {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1); }
}

.price-updating {
    animation: priceUpdate 0.5s ease-in-out;
}

/* Error state */
.currency-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    text-align: center;
}

/* Success state for currency change */
.currency-changed {
    position: relative;
}

.currency-changed::after {
    content: '✓';
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #10b981;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; transform: scale(0.8); }
    20%, 80% { opacity: 1; transform: scale(1); }
}
